-- =====================================================
-- SCRIPT SQL PARA EXECUTAR NO PHPMYADMIN OU PAINEL DE CONTROLE
-- Sistema de Biblioteca Digital CPA
-- =====================================================

-- Use este arquivo no painel de controle da sua hospedagem
-- Copie e cole este conteúdo no phpMyAdmin ou similar

-- =====================================================
-- 1. TABELA DE MANUAIS
-- =====================================================
CREATE TABLE IF NOT EXISTS `manuals` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `category` VARCHAR(100),
    `file_name` VARCHAR(255) NOT NULL,
    `file_path` VARCHAR(500) NOT NULL,
    `file_size` INT,
    `upload_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `downloads_count` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. TABELA DE ASSINANTES/USUÁRIOS
-- =====================================================
CREATE TABLE IF NOT EXISTS `subscribers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `phone` VARCHAR(20),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. TABELA DE PAGAMENTOS
-- =====================================================
CREATE TABLE IF NOT EXISTS `payments` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `subscriber_id` INT,
    `amount` DECIMAL(10,2) NOT NULL,
    `payment_method` VARCHAR(50),
    `receipt_filename` VARCHAR(255),
    `receipt_path` VARCHAR(500),
    `status` ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    `admin_notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`subscriber_id`) REFERENCES `subscribers`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. TABELA DE CÓDIGOS DE ACESSO
-- =====================================================
CREATE TABLE IF NOT EXISTS `codes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `subscriber_id` INT,
    `payment_id` INT,
    `code` VARCHAR(20) NOT NULL UNIQUE,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP,
    `used_count` INT DEFAULT 0,
    FOREIGN KEY (`subscriber_id`) REFERENCES `subscribers`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`payment_id`) REFERENCES `payments`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. TABELA DE DOWNLOADS (LOGS)
-- =====================================================
CREATE TABLE IF NOT EXISTS `downloads` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `manual_id` INT,
    `subscriber_id` INT,
    `code_id` INT,
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `download_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`manual_id`) REFERENCES `manuals`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`subscriber_id`) REFERENCES `subscribers`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`code_id`) REFERENCES `codes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. TABELA DE ADMINISTRADORES
-- =====================================================
CREATE TABLE IF NOT EXISTS `admins` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(100) NOT NULL UNIQUE,
    `password` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255),
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. INSERIR ADMIN PADRÃO
-- =====================================================
-- Senha: admin123 (hash bcrypt)
INSERT INTO `admins` (`username`, `password`, `email`) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>')
ON DUPLICATE KEY UPDATE `username` = `username`;

-- =====================================================
-- 8. ÍNDICES PARA MELHOR PERFORMANCE
-- =====================================================
CREATE INDEX `idx_manuals_active` ON `manuals`(`is_active`);
CREATE INDEX `idx_manuals_category` ON `manuals`(`category`);
CREATE INDEX `idx_payments_status` ON `payments`(`status`);
CREATE INDEX `idx_codes_active` ON `codes`(`is_active`);
CREATE INDEX `idx_codes_expires` ON `codes`(`expires_at`);
CREATE INDEX `idx_downloads_date` ON `downloads`(`download_date`);
CREATE INDEX `idx_subscribers_email` ON `subscribers`(`email`);

-- =====================================================
-- 9. DADOS DE EXEMPLO (OPCIONAL)
-- =====================================================
-- Inserir alguns manuais de exemplo
INSERT INTO `manuals` (`title`, `description`, `category`, `file_name`, `file_path`, `file_size`, `is_active`) VALUES
('Manual de Direito Constitucional', 'Guia completo para concursos públicos em Angola - Direito Constitucional', 'Direito', 'direito_constitucional.pdf', '/files/manuals/direito_constitucional.pdf', 2048576, 1),
('Manual de Administração Pública', 'Conceitos fundamentais de administração pública para concursos', 'Administração', 'admin_publica.pdf', '/files/manuals/admin_publica.pdf', 1536000, 1),
('Manual de Português', 'Gramática e interpretação de texto para concursos públicos', 'Português', 'portugues.pdf', '/files/manuals/portugues.pdf', 3072000, 1)
ON DUPLICATE KEY UPDATE `title` = `title`;

-- =====================================================
-- SCRIPT FINALIZADO
-- =====================================================
-- 
-- ✅ INSTRUÇÕES:
-- 1. Acesse o painel de controle da sua hospedagem
-- 2. Vá para phpMyAdmin ou similar
-- 3. Selecione o banco: kacennua_bibliotecacpa
-- 4. Copie e cole este script completo
-- 5. Execute o script
-- 
-- ✅ RESULTADO ESPERADO:
-- - 6 tabelas criadas (manuals, subscribers, payments, codes, downloads, admins)
-- - Admin padrão criado (admin/admin123)
-- - 3 manuais de exemplo inseridos
-- - Índices criados para performance
-- 
-- ✅ APÓS EXECUTAR:
-- - Faça upload dos arquivos PHP para seu servidor
-- - Teste com: test_connection.php
-- - Acesse admin: admin/login.php (admin/admin123)
-- 
-- ⚠️ IMPORTANTE:
-- - Altere a senha do admin após primeiro login
-- - Crie as pastas: uploads/receipts e files/manuals
-- - Configure permissões adequadas
-- 
-- =====================================================
