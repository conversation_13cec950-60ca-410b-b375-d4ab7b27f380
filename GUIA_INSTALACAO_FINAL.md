# 🚀 GUIA DE INSTALAÇÃO FINAL - Biblioteca Digital CPA

## ✅ SISTEMA COMPLETO PRONTO PARA INSTALAÇÃO

### 📋 **RESUMO DO QUE TEMOS:**
- ✅ **Sistema PHP completo** - 15+ arquivos funcionais
- ✅ **Banco MySQL configurado** - Script SQL pronto para execução
- ✅ **Demonstração funcionando** - http://localhost:3000
- ✅ **Documentação completa** - Guias e instruções detalhadas

---

## 🗄️ **PASSO 1: CONFIGURAR O BANCO DE DADOS**

### **Opção A: Via phpMyAdmin (RECOMENDADO)**

1. **Acesse seu painel de controle** (cPanel, Plesk, etc.)
2. **Abra o phpMyAdmin**
3. **Selecione o banco**: `kacennua_bibliotecacpa`
4. **Clique na aba "SQL"**
5. **Abra o arquivo**: `EXECUTAR_NO_PHPMYADMIN.sql`
6. **Copie todo o conteúdo** do arquivo
7. **Cole na área de SQL** do phpMyAdmin
8. **Clique em "Executar"**

### **Resultado Esperado:**
```
✅ 6 tabelas criadas (manuals, subscribers, payments, codes, downloads, admins)
✅ Admin padrão criado (admin/admin123)
✅ 3 manuais de exemplo inseridos
✅ Índices criados para performance
```

---

## 📁 **PASSO 2: UPLOAD DOS ARQUIVOS PHP**

### **Estrutura para Upload:**
```
/public_html/ (ou pasta do seu domínio)
├── index.php
├── manual.php
├── download_file.php
├── subscribe.php
├── upload_receipt.php
├── test_connection.php
├── .htaccess
├── config/
│   ├── database.php
│   ├── security.php
│   └── email.php
├── admin/
│   ├── login.php
│   ├── dashboard.php
│   ├── verify_payments.php
│   ├── manage_manuals.php
│   └── auth_check.php
├── uploads/
│   ├── .htaccess
│   └── receipts/ (criar pasta)
└── files/
    ├── .htaccess
    └── manuals/ (criar pasta)
```

### **Arquivos a NÃO fazer upload:**
- `demo-server.js`
- `execute-sql.js`
- `simulate-sql-execution.js`
- `node_modules/`
- `package.json`
- `*.md` (arquivos de documentação)

---

## 🔧 **PASSO 3: CONFIGURAR PERMISSÕES**

### **Via cPanel File Manager ou FTP:**
```bash
# Pastas de upload (leitura/escrita)
chmod 755 uploads/
chmod 755 uploads/receipts/
chmod 755 files/
chmod 755 files/manuals/

# Arquivos de proteção (leitura)
chmod 644 uploads/.htaccess
chmod 644 files/.htaccess
chmod 644 .htaccess

# Arquivos PHP (leitura/execução)
chmod 644 *.php
chmod 644 config/*.php
chmod 644 admin/*.php
```

---

## 🧪 **PASSO 4: TESTAR A INSTALAÇÃO**

### **1. Teste de Conexão:**
- Acesse: `http://seudominio.com/test_connection.php`
- Deve mostrar: ✅ Conexão estabelecida com sucesso!

### **2. Teste da Landing Page:**
- Acesse: `http://seudominio.com/`
- Deve mostrar: Lista de 3 manuais de exemplo

### **3. Teste do Admin:**
- Acesse: `http://seudominio.com/admin/login.php`
- Login: `admin` / Senha: `admin123`
- Deve mostrar: Dashboard com estatísticas

---

## 🔐 **PASSO 5: CONFIGURAÇÕES DE SEGURANÇA**

### **1. Alterar Senha do Admin:**
- Faça login no painel admin
- Vá em Configurações (quando implementado)
- Ou altere diretamente no banco:
```sql
UPDATE admins SET password = '$2y$10$NOVA_SENHA_HASH' WHERE username = 'admin';
```

### **2. Configurar Email:**
- Edite `config/email.php`
- Configure SMTP se necessário
- Teste envio de emails

### **3. Verificar .htaccess:**
- Confirme que os arquivos `.htaccess` estão funcionando
- Teste acessar: `http://seudominio.com/uploads/` (deve dar erro 403)

---

## 💰 **PASSO 6: CONFIGURAR PAGAMENTOS**

### **Editar Dados Bancários:**
1. **Abra**: `subscribe.php`
2. **Localize** as seções de métodos de pagamento (linhas ~180-200)
3. **Altere** os dados bancários:
```php
<div class="bank-info">
    <strong>Banco BAI</strong><br>
    Conta: SEU_NUMERO_CONTA<br>
    IBAN: SEU_IBAN
</div>
```

### **Alterar Preço:**
1. **Abra**: `subscribe.php`
2. **Localize** a linha com `value="15000"`
3. **Altere** para o valor desejado

---

## 📚 **PASSO 7: ADICIONAR MANUAIS**

### **Via Painel Admin:**
1. **Login**: `admin/login.php`
2. **Clique**: "Gerenciar Manuais"
3. **Preencha**: Título, categoria, descrição
4. **Upload**: Arquivo PDF/DOC
5. **Salve**: Manual fica disponível automaticamente

### **Formatos Aceitos:**
- PDF (recomendado)
- DOC/DOCX
- Tamanho máximo: 50MB

---

## 🎯 **PASSO 8: TESTAR FLUXO COMPLETO**

### **Teste de Usuário:**
1. **Acesse**: Página inicial
2. **Clique**: "Baixar Manual"
3. **Sem código**: Clique "Assine agora"
4. **Preencha**: Dados de assinatura
5. **Upload**: Comprovativo de teste
6. **Aguarde**: Mensagem de sucesso

### **Teste de Admin:**
1. **Login**: Painel admin
2. **Vá**: "Verificar Pagamentos"
3. **Visualize**: Comprovativo enviado
4. **Aprove**: Pagamento
5. **Verifique**: Email enviado (se configurado)

---

## 🚨 **SOLUÇÃO DE PROBLEMAS**

### **Erro de Conexão com Banco:**
- Verifique credenciais em `config/database.php`
- Confirme que o banco existe
- Teste conectividade com o servidor

### **Uploads não Funcionam:**
- Verifique permissões das pastas
- Confirme que `uploads/` e `files/` existem
- Teste upload de arquivo pequeno

### **Emails não Enviados:**
- Configure servidor SMTP
- Verifique função `mail()` do PHP
- Teste com email simples

### **Páginas em Branco:**
- Ative `display_errors` no PHP
- Verifique logs de erro do servidor
- Confirme que todas as extensões PHP estão ativas

---

## 📞 **SUPORTE E MANUTENÇÃO**

### **Logs Importantes:**
- Logs do servidor web (Apache/Nginx)
- Logs de erro do PHP
- Logs de email (se configurado)

### **Backup Regular:**
- Banco de dados (via phpMyAdmin)
- Arquivos de upload (`uploads/` e `files/`)
- Arquivos de configuração (`config/`)

### **Monitoramento:**
- Espaço em disco (uploads podem crescer)
- Performance do banco de dados
- Tentativas de acesso não autorizado

---

## 🎉 **SISTEMA PRONTO!**

Após seguir todos os passos, você terá:

✅ **Sistema funcionando** em produção  
✅ **Banco configurado** com todas as tabelas  
✅ **Admin funcional** para gerenciar tudo  
✅ **Fluxo completo** de assinatura e download  
✅ **Segurança implementada** contra ataques  
✅ **Interface responsiva** para todos os dispositivos  

**O sistema está completo e pronto para receber usuários!** 🚀

---

## 📋 **CHECKLIST FINAL**

- [ ] Banco de dados configurado
- [ ] Arquivos PHP enviados para servidor
- [ ] Permissões configuradas
- [ ] Teste de conexão OK
- [ ] Admin funcionando
- [ ] Senha do admin alterada
- [ ] Dados bancários configurados
- [ ] Manuais adicionados
- [ ] Fluxo de assinatura testado
- [ ] Emails configurados
- [ ] Backup inicial feito

**Quando todos os itens estiverem marcados, o sistema estará 100% operacional!**
