📁 ARQUIVOS PARA FAZER UPLOAD NO SERVIDOR
===========================================

✅ ARQUIVOS PRINCIPAIS (raiz do site):
├── index.php                    ← Landing page principal
├── manual.php                   ← Validação de código
├── download_file.php            ← Download protegido
├── subscribe.php                ← Formulário de assinatura
├── upload_receipt.php           ← Upload de comprovativo
├── test_connection.php          ← Teste de conexão
└── .htaccess                    ← Configurações de segurança

✅ PASTA CONFIG/:
├── config/database.php          ← Conexão com banco
├── config/security.php          ← Funções de segurança
└── config/email.php             ← Sistema de emails

✅ PASTA ADMIN/:
├── admin/login.php              ← Login administrativo
├── admin/dashboard.php          ← Dashboard principal
├── admin/verify_payments.php    ← Verificação de pagamentos
├── admin/manage_manuals.php     ← CRUD de manuais
└── admin/auth_check.php         ← Verificação de autenticação

✅ PASTAS PARA CRIAR (vazias):
├── uploads/                     ← Pasta para comprovativo
│   ├── .htaccess               ← Arquivo de proteção
│   └── receipts/               ← Subpasta (criar vazia)
└── files/                      ← Pasta para manuais
    ├── .htaccess               ← Arquivo de proteção
    └── manuals/                ← Subpasta (criar vazia)

❌ NÃO FAZER UPLOAD DESTES:
├── demo-server.js              ← Apenas para demonstração local
├── execute-sql.js              ← Script Node.js
├── simulate-sql-execution.js   ← Simulação
├── execute_sql.php             ← Script PHP local
├── node_modules/               ← Dependências Node.js
├── package.json                ← Configuração Node.js
├── package-lock.json           ← Lock file Node.js
├── *.md                        ← Arquivos de documentação
├── EXECUTAR_NO_PHPMYADMIN.sql  ← Já foi usado no banco
├── LISTA_ARQUIVOS_UPLOAD.txt   ← Este arquivo
└── GUIA_INSTALACAO_FINAL.md    ← Documentação

===========================================
ESTRUTURA FINAL NO SERVIDOR:
===========================================

/public_html/ (ou pasta do seu domínio)
├── index.php
├── manual.php
├── download_file.php
├── subscribe.php
├── upload_receipt.php
├── test_connection.php
├── .htaccess
├── config/
│   ├── database.php
│   ├── security.php
│   └── email.php
├── admin/
│   ├── login.php
│   ├── dashboard.php
│   ├── verify_payments.php
│   ├── manage_manuals.php
│   └── auth_check.php
├── uploads/
│   ├── .htaccess
│   └── receipts/ (pasta vazia)
└── files/
    ├── .htaccess
    └── manuals/ (pasta vazia)

===========================================
TOTAL DE ARQUIVOS: 15 arquivos PHP + 2 .htaccess
TOTAL DE PASTAS: 5 pastas (2 com subpastas)
===========================================
