# 📚 Biblioteca Digital CPA - Sistema de Manuais de Concursos Públicos

Sistema completo em PHP puro e MySQL para biblioteca digital com controle de acesso por código de assinatura mensal.

## 🎯 Funcionalidades

### Para Usuários:
- **Landing Page**: Lista todos os manuais disponíveis com busca
- **Download Protegido**: Acesso mediante código válido por 30 dias
- **Sistema de Assinatura**: Processo de pagamento com upload de comprovativo
- **Notificação Automática**: Recebimento de código por email após aprovação

### Para Administradores:
- **Painel Administrativo**: Dashboard com estatísticas completas
- **Verificação de Pagamentos**: Aprovação/rejeição com visualização de comprovativo
- **Gerenciamento de Manuais**: CRUD completo de manuais
- **Geração Automática de Códigos**: Códigos únicos válidos por 30 dias
- **Sistema de Logs**: Registro completo de downloads

## 🔧 Instalação

### 1. Configuração do Servidor
- PHP 7.4+ com extensões: PDO, GD, fileinfo
- MySQL 5.7+ ou MariaDB 10.2+
- Servidor web (Apache/Nginx)

### 2. Configuração do Banco de Dados
As credenciais já estão configuradas para o banco remoto:
- **Host**: *************
- **Banco**: kacennua_bibliotecacpa
- **Usuário**: kacennua_bibliotecacpa
- **Senha**: bibliotecacpa

### 3. Instalação dos Arquivos
1. Faça upload de todos os arquivos para o servidor web
2. Execute o script de configuração do banco:
```bash
php database/setup.php
```

### 4. Configuração de Permissões
```bash
chmod 755 uploads/
chmod 755 files/
chmod 644 uploads/.htaccess
chmod 644 files/.htaccess
```

## 📁 Estrutura do Projeto

```
/
├── index.php                 # Landing page principal
├── manual.php                # Página de validação de código
├── download_file.php         # Download protegido de arquivos
├── subscribe.php             # Formulário de assinatura
├── upload_receipt.php        # Upload de comprovativo
├── config/
│   ├── database.php          # Configuração do banco
│   ├── security.php          # Funções de segurança
│   └── email.php             # Sistema de emails
├── admin/
│   ├── login.php             # Login administrativo
│   ├── dashboard.php         # Dashboard principal
│   ├── verify_payments.php   # Verificação de pagamentos
│   ├── manage_manuals.php    # Gerenciamento de manuais
│   └── auth_check.php        # Verificação de autenticação
├── database/
│   ├── create_tables.sql     # Script SQL das tabelas
│   └── setup.php             # Script de configuração
├── uploads/
│   └── receipts/             # Comprovativo de pagamentos
└── files/
    └── manuals/              # Arquivos dos manuais
```

## 🔐 Segurança Implementada

- **Prepared Statements**: Proteção contra SQL Injection
- **Validação de Uploads**: Apenas formatos permitidos (JPG, PNG, PDF)
- **Arquivos Protegidos**: Manuais fora do webroot, servidos via PHP
- **Sanitização**: Todos os inputs são sanitizados
- **Tokens de Sessão**: Downloads protegidos por tokens temporários
- **Validação de Códigos**: Verificação de validade e expiração

## 👤 Acesso Administrativo

**Usuário padrão**: admin  
**Senha padrão**: admin123  

⚠️ **IMPORTANTE**: Altere a senha padrão após o primeiro login!

## 🚀 Fluxo de Uso

### Para Usuários:
1. Acesse a página inicial e escolha um manual
2. Insira o código de acesso (se tiver) ou clique em "Assinar"
3. Preencha os dados e faça o pagamento
4. Envie o comprovativo de pagamento
5. Aguarde aprovação (até 5 minutos)
6. Receba o código por email
7. Use o código para fazer downloads por 30 dias

### Para Administradores:
1. Acesse `/admin/login.php`
2. Faça login com as credenciais
3. No dashboard, clique em "Verificar Pagamentos"
4. Visualize o comprovativo e aprove/rejeite
5. O código é gerado e enviado automaticamente
6. Gerencie manuais em "Gerenciar Manuais"

## 📊 Banco de Dados

### Tabelas Principais:
- **manuals**: Informações dos manuais
- **subscribers**: Dados dos assinantes
- **payments**: Registros de pagamentos
- **codes**: Códigos de acesso gerados
- **downloads**: Logs de downloads
- **admins**: Usuários administrativos

## 📧 Configuração de Email

O sistema usa a função `mail()` do PHP. Para produção, configure:
1. Servidor SMTP no servidor
2. Ou substitua por biblioteca como PHPMailer
3. Ajuste o remetente em `config/email.php`

## 🔧 Personalização

### Alterar Preços:
Edite o valor em `subscribe.php` linha ~200

### Alterar Dados Bancários:
Edite as informações em `subscribe.php` nas seções de pagamento

### Alterar Layout:
Modifique os estilos CSS inline em cada arquivo

## 📱 Responsividade

O sistema é totalmente responsivo e funciona em:
- Desktop
- Tablets
- Smartphones

## 🐛 Solução de Problemas

### Erro de Conexão com Banco:
- Verifique as credenciais em `config/database.php`
- Teste a conectividade com o servidor remoto

### Uploads não Funcionam:
- Verifique permissões das pastas `uploads/` e `files/`
- Confirme se as extensões PHP estão ativas

### Emails não Enviados:
- Configure servidor SMTP
- Verifique logs do servidor web

## 📞 Suporte

Para suporte técnico ou dúvidas sobre implementação, consulte:
- Logs do servidor web
- Logs de erro do PHP
- Documentação do MySQL

---

**Desenvolvido para Biblioteca Digital CPA**  
Sistema completo de gerenciamento de manuais com controle de acesso por assinatura.
