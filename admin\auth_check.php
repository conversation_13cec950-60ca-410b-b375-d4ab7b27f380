<?php
/**
 * Verificação de autenticação administrativa
 * Sistema de Biblioteca Digital CPA
 */

session_start();

// Verificar se o admin está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Função para logout
function admin_logout() {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Processar logout se solicitado
if (isset($_GET['logout'])) {
    admin_logout();
}
