<?php
/**
 * Dashboard Administrativo
 * Sistema de Biblioteca Digital CPA
 */

require_once 'auth_check.php';
require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Estatísticas gerais
    $stats = [];
    
    // Total de manuais
    $query = "SELECT COUNT(*) as total FROM manuals WHERE is_active = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['manuals'] = $stmt->fetch()['total'];
    
    // Total de assinantes
    $query = "SELECT COUNT(*) as total FROM subscribers";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['subscribers'] = $stmt->fetch()['total'];
    
    // Pagamentos pendentes
    $query = "SELECT COUNT(*) as total FROM payments WHERE status = 'pending'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['pending_payments'] = $stmt->fetch()['total'];
    
    // Códigos ativos
    $query = "SELECT COUNT(*) as total FROM codes WHERE is_active = 1 AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['active_codes'] = $stmt->fetch()['total'];
    
    // Downloads hoje
    $query = "SELECT COUNT(*) as total FROM downloads WHERE DATE(download_date) = CURDATE()";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['downloads_today'] = $stmt->fetch()['total'];
    
} catch (Exception $e) {
    $stats = [
        'manuals' => 0,
        'subscribers' => 0,
        'pending_payments' => 0,
        'active_codes' => 0,
        'downloads_today' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - Biblioteca Digital CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .action-card h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .action-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            transition: opacity 0.3s;
        }
        
        .action-btn:hover {
            opacity: 0.9;
        }
        
        .urgent {
            border-left: 4px solid #ff6b6b;
        }
        
        .urgent .stat-number {
            color: #ff6b6b;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📊 Dashboard Administrativo</h1>
            <div class="user-info">
                <span>👤 <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="?logout=1" class="logout-btn">Sair</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['manuals']; ?></div>
                <div class="stat-label">Manuais Ativos</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['subscribers']; ?></div>
                <div class="stat-label">Total Assinantes</div>
            </div>
            
            <div class="stat-card <?php echo $stats['pending_payments'] > 0 ? 'urgent' : ''; ?>">
                <div class="stat-number"><?php echo $stats['pending_payments']; ?></div>
                <div class="stat-label">Pagamentos Pendentes</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active_codes']; ?></div>
                <div class="stat-label">Códigos Ativos</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['downloads_today']; ?></div>
                <div class="stat-label">Downloads Hoje</div>
            </div>
        </div>
        
        <div class="actions-grid">
            <div class="action-card <?php echo $stats['pending_payments'] > 0 ? 'urgent' : ''; ?>">
                <h3>💳 Verificar Pagamentos</h3>
                <p>Analise e aprove/rejeite pagamentos pendentes. Códigos são gerados automaticamente após aprovação.</p>
                <a href="verify_payments.php" class="action-btn">
                    Verificar Pagamentos <?php echo $stats['pending_payments'] > 0 ? "({$stats['pending_payments']})" : ''; ?>
                </a>
            </div>
            
            <div class="action-card">
                <h3>📚 Gerenciar Manuais</h3>
                <p>Adicione, edite ou remova manuais da biblioteca. Controle quais manuais estão disponíveis para download.</p>
                <a href="manage_manuals.php" class="action-btn">Gerenciar Manuais</a>
            </div>
            
            <div class="action-card">
                <h3>👥 Assinantes</h3>
                <p>Visualize informações dos assinantes, histórico de pagamentos e códigos gerados.</p>
                <a href="manage_subscribers.php" class="action-btn">Ver Assinantes</a>
            </div>
            
            <div class="action-card">
                <h3>📊 Relatórios</h3>
                <p>Acesse relatórios detalhados de downloads, pagamentos e uso da plataforma.</p>
                <a href="reports.php" class="action-btn">Ver Relatórios</a>
            </div>
            
            <div class="action-card">
                <h3>🔧 Configurações</h3>
                <p>Altere configurações do sistema, preços e informações de pagamento.</p>
                <a href="settings.php" class="action-btn">Configurações</a>
            </div>
            
            <div class="action-card">
                <h3>🌐 Ver Site</h3>
                <p>Acesse o site público para ver como os usuários visualizam a biblioteca.</p>
                <a href="../index.php" class="action-btn" target="_blank">Abrir Site</a>
            </div>
        </div>
    </div>
</body>
</html>
