<?php
/**
 * Gerenciamento de Manuais
 * Sistema de Biblioteca Digital CPA
 */

require_once 'auth_check.php';
require_once '../config/database.php';
require_once '../config/security.php';

$message = '';
$message_type = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        if ($action === 'add') {
            // Adicionar novo manual
            $title = Security::sanitizeInput($_POST['title'] ?? '');
            $description = Security::sanitizeInput($_POST['description'] ?? '');
            $category = Security::sanitizeInput($_POST['category'] ?? '');
            
            if (empty($title)) {
                throw new Exception("Título é obrigatório.");
            }
            
            // Validar upload do arquivo
            if (!isset($_FILES['manual_file']) || $_FILES['manual_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("Arquivo do manual é obrigatório.");
            }
            
            $upload_result = Security::validateUpload($_FILES['manual_file']);
            if (!$upload_result['valid']) {
                throw new Exception($upload_result['message']);
            }
            
            // Criar diretório se não existir
            $upload_dir = '../files/manuals/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Gerar nome seguro para o arquivo
            $secure_filename = Security::generateSecureFilename($_FILES['manual_file']['name']);
            $upload_path = $upload_dir . $secure_filename;
            
            if (move_uploaded_file($_FILES['manual_file']['tmp_name'], $upload_path)) {
                // Inserir no banco
                $query = "INSERT INTO manuals (title, description, category, file_name, file_path, file_size) VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($query);
                $stmt->execute([
                    $title,
                    $description,
                    $category,
                    $_FILES['manual_file']['name'],
                    $upload_path,
                    $_FILES['manual_file']['size']
                ]);
                
                $message = "Manual adicionado com sucesso!";
                $message_type = 'success';
            } else {
                throw new Exception("Erro ao fazer upload do arquivo.");
            }
            
        } elseif ($action === 'toggle_status') {
            // Ativar/desativar manual
            $manual_id = (int)($_POST['manual_id'] ?? 0);
            $new_status = (int)($_POST['new_status'] ?? 0);
            
            $query = "UPDATE manuals SET is_active = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$new_status, $manual_id]);
            
            $status_text = $new_status ? 'ativado' : 'desativado';
            $message = "Manual {$status_text} com sucesso!";
            $message_type = 'success';
            
        } elseif ($action === 'delete') {
            // Excluir manual
            $manual_id = (int)($_POST['manual_id'] ?? 0);
            
            // Buscar arquivo para deletar
            $query = "SELECT file_path FROM manuals WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$manual_id]);
            $manual = $stmt->fetch();
            
            if ($manual && file_exists($manual['file_path'])) {
                unlink($manual['file_path']);
            }
            
            // Deletar do banco
            $query = "DELETE FROM manuals WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$manual_id]);
            
            $message = "Manual excluído com sucesso!";
            $message_type = 'success';
        }
        
    } catch (Exception $e) {
        $message = "Erro: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Buscar todos os manuais
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT * FROM manuals ORDER BY created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $manuals = $stmt->fetchAll();
    
} catch (Exception $e) {
    $manuals = [];
}

// Função para formatar tamanho do arquivo
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Manuais - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .add-manual-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .file-input {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .file-input:hover {
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: opacity 0.3s;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 14px;
        }
        
        .manuals-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .table {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 10px 8px;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📚 Gerenciar Manuais</h1>
            <a href="dashboard.php" class="back-btn">← Dashboard</a>
        </div>
    </div>
    
    <div class="container">
        <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- Formulário para adicionar manual -->
        <div class="add-manual-card">
            <h2>➕ Adicionar Novo Manual</h2>
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="add">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="title" class="form-label">Título *</label>
                        <input type="text" id="title" name="title" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="category" class="form-label">Categoria</label>
                        <input type="text" id="category" name="category" class="form-input" 
                               placeholder="Ex: Concursos Públicos, Direito, etc.">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description" class="form-label">Descrição</label>
                    <textarea id="description" name="description" class="form-textarea" 
                              placeholder="Descrição detalhada do manual..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="manual_file" class="form-label">Arquivo do Manual *</label>
                    <div class="file-input" onclick="document.getElementById('manual_file').click()">
                        <p>📁 Clique para selecionar o arquivo</p>
                        <p><small>Formatos aceitos: PDF, DOC, DOCX (máx. 50MB)</small></p>
                        <input type="file" id="manual_file" name="manual_file" style="display: none;" 
                               accept=".pdf,.doc,.docx" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    ➕ Adicionar Manual
                </button>
            </form>
        </div>
        
        <!-- Lista de manuais -->
        <div class="manuals-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Título</th>
                        <th>Categoria</th>
                        <th>Tamanho</th>
                        <th>Downloads</th>
                        <th>Status</th>
                        <th>Data</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($manuals)): ?>
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 50px;">
                                Nenhum manual cadastrado.
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($manuals as $manual): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($manual['title']); ?></strong>
                                    <?php if ($manual['description']): ?>
                                        <br><small style="color: #666;"><?php echo htmlspecialchars(substr($manual['description'], 0, 100)); ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($manual['category'] ?: 'Sem categoria'); ?></td>
                                <td><?php echo $manual['file_size'] ? formatFileSize($manual['file_size']) : 'N/A'; ?></td>
                                <td><?php echo $manual['downloads_count']; ?></td>
                                <td>
                                    <span class="status-badge <?php echo $manual['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $manual['is_active'] ? 'Ativo' : 'Inativo'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($manual['created_at'])); ?></td>
                                <td>
                                    <div class="actions">
                                        <!-- Toggle status -->
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="manual_id" value="<?php echo $manual['id']; ?>">
                                            <input type="hidden" name="new_status" value="<?php echo $manual['is_active'] ? 0 : 1; ?>">
                                            <button type="submit" class="btn btn-sm <?php echo $manual['is_active'] ? 'btn-warning' : 'btn-success'; ?>"
                                                    onclick="return confirm('<?php echo $manual['is_active'] ? 'Desativar' : 'Ativar'; ?> este manual?')">
                                                <?php echo $manual['is_active'] ? '⏸️' : '▶️'; ?>
                                            </button>
                                        </form>
                                        
                                        <!-- Delete -->
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="manual_id" value="<?php echo $manual['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Excluir este manual permanentemente? Esta ação não pode ser desfeita.')">
                                                🗑️
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // Mostrar nome do arquivo selecionado
        document.getElementById('manual_file').addEventListener('change', function(e) {
            const fileInput = e.target;
            const fileInputContainer = fileInput.parentElement;
            
            if (fileInput.files.length > 0) {
                const fileName = fileInput.files[0].name;
                const fileSize = (fileInput.files[0].size / 1024 / 1024).toFixed(2);
                fileInputContainer.innerHTML = `
                    <p>📄 ${fileName}</p>
                    <p><small>Tamanho: ${fileSize} MB</small></p>
                `;
            }
        });
    </script>
</body>
</html>
