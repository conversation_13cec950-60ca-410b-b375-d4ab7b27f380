<?php
/**
 * Verificação de Pagamentos
 * Sistema de Biblioteca Digital CPA
 */

require_once 'auth_check.php';
require_once '../config/database.php';
require_once '../config/security.php';
require_once '../config/email.php';

$message = '';
$message_type = '';

// Processar aprovação/rejeição
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_id = (int)($_POST['payment_id'] ?? 0);
    $action = $_POST['action'] ?? '';
    $admin_notes = Security::sanitizeInput($_POST['admin_notes'] ?? '');
    
    if ($payment_id > 0 && in_array($action, ['approve', 'reject'])) {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            // Buscar dados do pagamento
            $query = "SELECT p.*, s.name, s.email 
                      FROM payments p 
                      JOIN subscribers s ON p.subscriber_id = s.id 
                      WHERE p.id = ? AND p.status = 'pending'";
            $stmt = $conn->prepare($query);
            $stmt->execute([$payment_id]);
            $payment = $stmt->fetch();
            
            if ($payment) {
                if ($action === 'approve') {
                    // Aprovar pagamento
                    $query = "UPDATE payments SET status = 'approved', admin_notes = ? WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$admin_notes, $payment_id]);
                    
                    // Gerar código de acesso
                    $access_code = Security::generateCode();
                    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
                    
                    $query = "INSERT INTO codes (subscriber_id, payment_id, code, expires_at) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$payment['subscriber_id'], $payment_id, $access_code, $expires_at]);
                    
                    // Enviar email com código
                    if (EmailSender::sendAccessCode($payment['email'], $payment['name'], $access_code)) {
                        $message = "Pagamento aprovado e código enviado por email!";
                        $message_type = 'success';
                    } else {
                        $message = "Pagamento aprovado, mas erro ao enviar email. Código: {$access_code}";
                        $message_type = 'warning';
                    }
                    
                } else {
                    // Rejeitar pagamento
                    $query = "UPDATE payments SET status = 'rejected', admin_notes = ? WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$admin_notes, $payment_id]);
                    
                    // Enviar email de rejeição
                    EmailSender::sendRejectionNotice($payment['email'], $payment['name']);
                    
                    $message = "Pagamento rejeitado e usuário notificado.";
                    $message_type = 'success';
                }
            } else {
                $message = "Pagamento não encontrado ou já processado.";
                $message_type = 'error';
            }
            
        } catch (Exception $e) {
            $message = "Erro ao processar pagamento: " . $e->getMessage();
            $message_type = 'error';
        }
    }
}

// Buscar pagamentos pendentes
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT p.*, s.name, s.email, s.phone 
              FROM payments p 
              JOIN subscribers s ON p.subscriber_id = s.id 
              WHERE p.status = 'pending' 
              ORDER BY p.created_at ASC";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $pending_payments = $stmt->fetchAll();
    
} catch (Exception $e) {
    $pending_payments = [];
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificar Pagamentos - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .payments-grid {
            display: grid;
            gap: 20px;
        }
        
        .payment-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .payment-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .payment-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .payment-body {
            padding: 20px;
        }
        
        .receipt-section {
            margin-bottom: 20px;
        }
        
        .receipt-image {
            max-width: 100%;
            max-height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-approve {
            background: #28a745;
            color: white;
        }
        
        .btn-reject {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .notes-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 10px;
            resize: vertical;
            min-height: 80px;
        }
        
        .no-payments {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            margin: 5% auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
        }
        
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: white;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .payment-info {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>💳 Verificar Pagamentos</h1>
            <a href="dashboard.php" class="back-btn">← Dashboard</a>
        </div>
    </div>
    
    <div class="container">
        <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (empty($pending_payments)): ?>
            <div class="no-payments">
                <h2>✅ Nenhum pagamento pendente</h2>
                <p>Todos os pagamentos foram processados.</p>
            </div>
        <?php else: ?>
            <div class="payments-grid">
                <?php foreach ($pending_payments as $payment): ?>
                    <div class="payment-card">
                        <div class="payment-header">
                            <div class="payment-info">
                                <div>
                                    <strong>👤 Cliente:</strong> <?php echo htmlspecialchars($payment['name']); ?><br>
                                    <strong>📧 Email:</strong> <?php echo htmlspecialchars($payment['email']); ?><br>
                                    <?php if ($payment['phone']): ?>
                                        <strong>📱 Telefone:</strong> <?php echo htmlspecialchars($payment['phone']); ?><br>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>💰 Valor:</strong> <?php echo number_format($payment['amount'], 2, ',', '.'); ?> Kz<br>
                                    <strong>💳 Método:</strong> <?php echo htmlspecialchars($payment['payment_method']); ?><br>
                                    <strong>📅 Data:</strong> <?php echo date('d/m/Y H:i', strtotime($payment['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="payment-body">
                            <?php if ($payment['receipt_path'] && file_exists($payment['receipt_path'])): ?>
                                <div class="receipt-section">
                                    <h4>📄 Comprovativo:</h4>
                                    <img src="<?php echo $payment['receipt_path']; ?>" 
                                         alt="Comprovativo" 
                                         class="receipt-image"
                                         onclick="openModal('<?php echo $payment['receipt_path']; ?>')">
                                    <p><small>Clique na imagem para ampliar</small></p>
                                </div>
                            <?php else: ?>
                                <div class="receipt-section">
                                    <p style="color: #dc3545;">⚠️ Comprovativo não encontrado</p>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" style="margin-top: 20px;">
                                <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                                
                                <label for="notes_<?php echo $payment['id']; ?>"><strong>Observações:</strong></label>
                                <textarea name="admin_notes" 
                                          id="notes_<?php echo $payment['id']; ?>" 
                                          class="notes-input" 
                                          placeholder="Adicione observações sobre este pagamento..."></textarea>
                                
                                <div class="actions">
                                    <button type="submit" name="action" value="approve" class="btn btn-approve"
                                            onclick="return confirm('Aprovar este pagamento? Um código será gerado e enviado por email.')">
                                        ✅ Aprovar
                                    </button>
                                    <button type="submit" name="action" value="reject" class="btn btn-reject"
                                            onclick="return confirm('Rejeitar este pagamento? O cliente será notificado por email.')">
                                        ❌ Rejeitar
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Modal para visualizar comprovativo -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(imageSrc) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = imageSrc;
        }
        
        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }
        
        // Fechar modal ao clicar fora da imagem
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
