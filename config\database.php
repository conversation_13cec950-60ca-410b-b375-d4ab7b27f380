<?php
/**
 * Configuração de conexão com banco de dados MySQL remoto
 * Sistema de Biblioteca Digital - Manuais de Concursos Públicos Angola
 */

class Database {
    // Configurações do banco remoto
    private $host = '*************';
    private $db_name = 'kacennua_bibliotecacpa';
    private $username = 'kacennua_bibliotecacpa';
    private $password = 'bibliotecacpa';
    private $conn;

    /**
     * Estabelece conexão com o banco de dados
     * @return PDO|null
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch(PDOException $exception) {
            error_log("Erro de conexão com banco: " . $exception->getMessage());
            die("Erro interno do servidor. Tente novamente mais tarde.");
        }

        return $this->conn;
    }

    /**
     * Testa a conexão com o banco
     * @return bool
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            return $conn !== null;
        } catch(Exception $e) {
            return false;
        }
    }
}
?>
