<?php
/**
 * Classe para envio de emails
 * Sistema de Biblioteca Digital
 */

class EmailSender {
    
    /**
     * Envia email com código de acesso
     * @param string $to
     * @param string $name
     * @param string $code
     * @return bool
     */
    public static function sendAccessCode($to, $name, $code) {
        $subject = "Seu código de acesso - Biblioteca Digital CPA";
        
        $message = "
        <html>
        <head>
            <title>Código de Acesso - Biblioteca Digital</title>
        </head>
        <body>
            <h2>Olá, {$name}!</h2>
            <p>Seu pagamento foi aprovado com sucesso!</p>
            <p>Seu código de acesso é: <strong style='font-size: 18px; color: #007bff;'>{$code}</strong></p>
            <p>Este código é válido por 30 dias a partir de hoje.</p>
            <p>Use este código para fazer download dos manuais em nossa biblioteca digital.</p>
            <br>
            <p>Atenciosamente,<br>Equipe Biblioteca Digital CPA</p>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: <EMAIL>" . "\r\n";
        
        return mail($to, $subject, $message, $headers);
    }
    
    /**
     * Envia notificação de pagamento rejeitado
     * @param string $to
     * @param string $name
     * @return bool
     */
    public static function sendRejectionNotice($to, $name) {
        $subject = "Pagamento não aprovado - Biblioteca Digital CPA";
        
        $message = "
        <html>
        <head>
            <title>Pagamento não aprovado</title>
        </head>
        <body>
            <h2>Olá, {$name}!</h2>
            <p>Infelizmente não conseguimos aprovar seu pagamento.</p>
            <p>Possíveis motivos:</p>
            <ul>
                <li>Comprovativo ilegível</li>
                <li>Valor incorreto</li>
                <li>Dados bancários não conferem</li>
            </ul>
            <p>Por favor, tente novamente ou entre em contato conosco.</p>
            <br>
            <p>Atenciosamente,<br>Equipe Biblioteca Digital CPA</p>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: <EMAIL>" . "\r\n";
        
        return mail($to, $subject, $message, $headers);
    }
}
