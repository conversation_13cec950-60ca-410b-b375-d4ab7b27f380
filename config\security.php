<?php
/**
 * Classe de segurança para validação e sanitização
 * Sistema de Biblioteca Digital
 */

class Security {
    
    /**
     * Sanitiza entrada de dados
     * @param string $data
     * @return string
     */
    public static function sanitizeInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        return $data;
    }

    /**
     * Valida email
     * @param string $email
     * @return bool
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Gera código único para assinatura
     * @return string
     */
    public static function generateCode() {
        return strtoupper(substr(md5(uniqid(rand(), true)), 0, 12));
    }

    /**
     * Valida upload de arquivo
     * @param array $file
     * @return array
     */
    public static function validateUpload($file) {
        $allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        $result = ['valid' => false, 'message' => ''];
        
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $result['message'] = 'Nenhum arquivo foi enviado.';
            return $result;
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = 'Erro no upload do arquivo.';
            return $result;
        }
        
        if ($file['size'] > $maxSize) {
            $result['message'] = 'Arquivo muito grande. Máximo 5MB.';
            return $result;
        }
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            $result['message'] = 'Tipo de arquivo não permitido. Use JPG, PNG ou PDF.';
            return $result;
        }
        
        $result['valid'] = true;
        $result['mime_type'] = $mimeType;
        return $result;
    }

    /**
     * Gera nome seguro para arquivo
     * @param string $originalName
     * @return string
     */
    public static function generateSecureFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        return uniqid('file_', true) . '.' . strtolower($extension);
    }

    /**
     * Verifica se código está válido (dentro de 30 dias)
     * @param string $createdDate
     * @return bool
     */
    public static function isCodeValid($createdDate) {
        $created = new DateTime($createdDate);
        $now = new DateTime();
        $interval = $created->diff($now);
        return $interval->days <= 30;
    }
}
