<?php
/**
 * Script para configurar permissões automaticamente
 * Execute este arquivo UMA VEZ após fazer upload
 */

echo "<h1>🔧 Configurando Permissões - Biblioteca Digital CPA</h1>";
echo "<hr>";

$errors = [];
$success = [];

// Função para definir permissões
function setPermissions($path, $permission, $description) {
    global $errors, $success;
    
    if (file_exists($path)) {
        if (chmod($path, $permission)) {
            $success[] = "✅ $description: $path (" . decoct($permission) . ")";
        } else {
            $errors[] = "❌ Erro ao definir permissão para: $path";
        }
    } else {
        $errors[] = "⚠️ Arquivo/pasta não encontrado: $path";
    }
}

// Função para criar pasta se não existir
function createDirectory($path, $permission = 0755) {
    global $errors, $success;
    
    if (!file_exists($path)) {
        if (mkdir($path, $permission, true)) {
            $success[] = "✅ Pasta criada: $path";
            return true;
        } else {
            $errors[] = "❌ Erro ao criar pasta: $path";
            return false;
        }
    } else {
        $success[] = "✅ Pasta já existe: $path";
        return true;
    }
}

echo "<h2>📁 Criando Pastas Necessárias...</h2>";

// Criar pastas necessárias
createDirectory('uploads', 0755);
createDirectory('uploads/receipts', 0755);
createDirectory('files', 0755);
createDirectory('files/manuals', 0755);

echo "<h2>🔒 Configurando Permissões...</h2>";

// Permissões para pastas de upload (leitura/escrita)
setPermissions('uploads', 0755, 'Pasta uploads');
setPermissions('uploads/receipts', 0755, 'Pasta receipts');
setPermissions('files', 0755, 'Pasta files');
setPermissions('files/manuals', 0755, 'Pasta manuals');

// Permissões para arquivos de proteção
setPermissions('uploads/.htaccess', 0644, 'Proteção uploads');
setPermissions('files/.htaccess', 0644, 'Proteção files');
setPermissions('.htaccess', 0644, 'Proteção raiz');

// Permissões para arquivos PHP principais
$phpFiles = [
    'index.php',
    'manual.php', 
    'download_file.php',
    'subscribe.php',
    'upload_receipt.php',
    'test_connection.php'
];

foreach ($phpFiles as $file) {
    setPermissions($file, 0644, "Arquivo PHP");
}

// Permissões para arquivos de configuração
$configFiles = [
    'config/database.php',
    'config/security.php',
    'config/email.php'
];

foreach ($configFiles as $file) {
    setPermissions($file, 0644, "Arquivo config");
}

// Permissões para arquivos admin
$adminFiles = [
    'admin/login.php',
    'admin/dashboard.php',
    'admin/verify_payments.php',
    'admin/manage_manuals.php',
    'admin/auth_check.php'
];

foreach ($adminFiles as $file) {
    setPermissions($file, 0644, "Arquivo admin");
}

// Mostrar resultados
echo "<hr>";
echo "<h2>📊 Resultado:</h2>";

if (!empty($success)) {
    echo "<h3 style='color: green;'>✅ Sucessos:</h3>";
    echo "<ul>";
    foreach ($success as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>❌ Erros:</h3>";
    echo "<ul>";
    foreach ($errors as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul>";
}

echo "<hr>";

if (empty($errors)) {
    echo "<h2 style='color: green;'>🎉 PERMISSÕES CONFIGURADAS COM SUCESSO!</h2>";
    echo "<p>Todas as permissões foram definidas corretamente.</p>";
} else {
    echo "<h2 style='color: orange;'>⚠️ CONFIGURAÇÃO PARCIAL</h2>";
    echo "<p>Algumas permissões podem precisar ser ajustadas manualmente.</p>";
}

echo "<h3>🚀 Próximos Passos:</h3>";
echo "<ol>";
echo "<li><strong>Teste a conexão:</strong> <a href='test_connection.php'>test_connection.php</a></li>";
echo "<li><strong>Acesse o admin:</strong> <a href='admin/login.php'>admin/login.php</a> (admin/admin123)</li>";
echo "<li><strong>Veja o site:</strong> <a href='index.php'>index.php</a></li>";
echo "<li><strong>Delete este arquivo</strong> após usar (por segurança)</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Script executado em: " . date('d/m/Y H:i:s') . "</small></p>";
?>
