# Script PowerShell para criar ZIP do sistema
# Execute: powershell -ExecutionPolicy Bypass -File criar_zip.ps1

Write-Host "🚀 Criando ZIP para Upload - Biblioteca Digital CPA" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Yellow

# Nome do arquivo ZIP
$zipName = "biblioteca-digital-cpa.zip"
$tempFolder = "temp_upload"

# Criar pasta temporária
if (Test-Path $tempFolder) {
    Remove-Item $tempFolder -Recurse -Force
}
New-Item -ItemType Directory -Path $tempFolder | Out-Null

Write-Host "📁 Organizando arquivos..." -ForegroundColor Cyan

# Copiar arquivos principais para a raiz
$mainFiles = @(
    "index.php",
    "manual.php", 
    "download_file.php",
    "subscribe.php",
    "upload_receipt.php",
    "test_connection.php",
    "configurar_permissoes.php"
)

foreach ($file in $mainFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $tempFolder
        Write-Host "✅ Copiado: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Não encontrado: $file" -ForegroundColor Red
    }
}

# Copiar pasta config
if (Test-Path "config") {
    Copy-Item "config" -Destination $tempFolder -Recurse
    Write-Host "✅ Copiada pasta: config/" -ForegroundColor Green
} else {
    Write-Host "❌ Pasta não encontrada: config/" -ForegroundColor Red
}

# Copiar pasta admin
if (Test-Path "admin") {
    Copy-Item "admin" -Destination $tempFolder -Recurse
    Write-Host "✅ Copiada pasta: admin/" -ForegroundColor Green
} else {
    Write-Host "❌ Pasta não encontrada: admin/" -ForegroundColor Red
}

# Criar estrutura de pastas vazias
Write-Host "📁 Criando estrutura de pastas..." -ForegroundColor Cyan

New-Item -ItemType Directory -Path "$tempFolder\uploads" -Force | Out-Null
New-Item -ItemType Directory -Path "$tempFolder\uploads\receipts" -Force | Out-Null
New-Item -ItemType Directory -Path "$tempFolder\files" -Force | Out-Null
New-Item -ItemType Directory -Path "$tempFolder\files\manuals" -Force | Out-Null

# Criar arquivos .htaccess
Write-Host "🔒 Criando arquivos de proteção..." -ForegroundColor Cyan

# .htaccess para uploads
$htaccessUploads = @"
# Proteção da pasta uploads
# Bloqueia acesso direto aos arquivos
Order Deny,Allow
Deny from all

# Permite apenas scripts PHP acessarem
<Files "*.php">
    Allow from all
</Files>
"@

$htaccessUploads | Out-File -FilePath "$tempFolder\uploads\.htaccess" -Encoding UTF8

# .htaccess para files
$htaccessFiles = @"
# Proteção da pasta files
# Bloqueia acesso direto aos manuais
Order Deny,Allow
Deny from all

# Permite apenas scripts PHP acessarem
<Files "*.php">
    Allow from all
</Files>
"@

$htaccessFiles | Out-File -FilePath "$tempFolder\files\.htaccess" -Encoding UTF8

# .htaccess principal
$htaccessMain = @"
# Configurações de segurança principais
RewriteEngine On

# Bloquear acesso a arquivos sensíveis
<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Headers de segurança
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Configurações PHP
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
"@

$htaccessMain | Out-File -FilePath "$tempFolder\.htaccess" -Encoding UTF8

Write-Host "✅ Arquivos .htaccess criados" -ForegroundColor Green

# Criar arquivo README para o ZIP
$readmeZip = @"
🚀 BIBLIOTECA DIGITAL CPA - ARQUIVOS PARA UPLOAD
===============================================

✅ BANCO DE DADOS: JÁ CONFIGURADO
✅ ARQUIVOS: PRONTOS PARA UPLOAD

📋 INSTRUÇÕES:
1. Extraia este ZIP na pasta public_html do seu servidor
2. Acesse: http://seudominio.com/configurar_permissoes.php
3. Execute o script de permissões
4. Teste: http://seudominio.com/test_connection.php
5. Admin: http://seudominio.com/admin/login.php (admin/admin123)

📁 ESTRUTURA INCLUÍDA:
├── Arquivos PHP principais (7 arquivos)
├── config/ (3 arquivos de configuração)
├── admin/ (5 arquivos administrativos)
├── uploads/ (pasta protegida + subpasta receipts)
├── files/ (pasta protegida + subpasta manuals)
└── Arquivos .htaccess de segurança

🎯 APÓS EXTRAIR:
- Total: 15 arquivos PHP
- Total: 6 pastas criadas
- Sistema 100% funcional

⚠️ IMPORTANTE:
- Delete o arquivo configurar_permissoes.php após usar
- Altere a senha do admin após primeiro login
- Configure os dados bancários em subscribe.php

Data de criação: $(Get-Date -Format 'dd/MM/yyyy HH:mm:ss')
"@

$readmeZip | Out-File -FilePath "$tempFolder\LEIA-ME.txt" -Encoding UTF8

# Criar o ZIP
Write-Host "📦 Criando arquivo ZIP..." -ForegroundColor Cyan

if (Test-Path $zipName) {
    Remove-Item $zipName -Force
}

try {
    Compress-Archive -Path "$tempFolder\*" -DestinationPath $zipName -Force
    Write-Host "✅ ZIP criado com sucesso: $zipName" -ForegroundColor Green
    
    # Mostrar tamanho do arquivo
    $zipSize = (Get-Item $zipName).Length
    $zipSizeMB = [math]::Round($zipSize / 1MB, 2)
    Write-Host "📊 Tamanho do arquivo: $zipSizeMB MB" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Erro ao criar ZIP: $($_.Exception.Message)" -ForegroundColor Red
}

# Limpar pasta temporária
Remove-Item $tempFolder -Recurse -Force

Write-Host "`n🎉 PRONTO PARA UPLOAD!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Yellow
Write-Host "1. Use o arquivo: $zipName" -ForegroundColor White
Write-Host "2. Faça upload via cPanel File Manager" -ForegroundColor White
Write-Host "3. Extraia na pasta public_html" -ForegroundColor White
Write-Host "4. Execute configurar_permissoes.php" -ForegroundColor White
Write-Host "=================================================" -ForegroundColor Yellow

# Mostrar conteúdo do ZIP
Write-Host "`n📋 CONTEÚDO DO ZIP:" -ForegroundColor Cyan
if (Get-Command 7z -ErrorAction SilentlyContinue) {
    7z l $zipName
} else {
    Write-Host "Para ver o conteúdo, extraia o ZIP em uma pasta de teste" -ForegroundColor Yellow
}

Write-Host "`nScript finalizado!" -ForegroundColor Green
