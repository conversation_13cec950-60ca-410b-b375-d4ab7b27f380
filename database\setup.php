<?php
/**
 * Script para configuração inicial do banco de dados
 * Execute este arquivo uma vez para criar todas as tabelas
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Lê o arquivo SQL
    $sql = file_get_contents('create_tables.sql');
    
    // Divide as queries por ponto e vírgula
    $queries = explode(';', $sql);
    
    $success = 0;
    $errors = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query)) {
            try {
                $conn->exec($query);
                $success++;
                echo "✓ Query executada com sucesso\n";
            } catch (PDOException $e) {
                $errors++;
                echo "✗ Erro na query: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== RESULTADO ===\n";
    echo "Queries executadas com sucesso: $success\n";
    echo "Erros encontrados: $errors\n";
    
    if ($errors === 0) {
        echo "\n🎉 Banco de dados configurado com sucesso!\n";
        echo "Você pode agora usar o sistema.\n";
        
        // Criar diretórios necessários
        $directories = [
            '../uploads/receipts',
            '../uploads/manuals',
            '../files/manuals'
        ];
        
        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
                echo "✓ Diretório criado: $dir\n";
            }
        }
        
        // Criar arquivo .htaccess para proteger uploads
        $htaccess_content = "Order Deny,Allow\nDeny from all";
        file_put_contents('../uploads/.htaccess', $htaccess_content);
        file_put_contents('../files/.htaccess', $htaccess_content);
        echo "✓ Arquivos de proteção criados\n";
        
    } else {
        echo "\n⚠️  Alguns erros foram encontrados. Verifique as mensagens acima.\n";
    }
    
} catch (Exception $e) {
    echo "Erro fatal: " . $e->getMessage() . "\n";
}
