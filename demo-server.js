const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// Dados de demonstração
const demoManuals = [
    {
        id: 1,
        title: "Manual de Direito Constitucional",
        description: "Guia completo para concursos públicos em Angola - Direito Constitucional",
        category: "Direito",
        file_size: 2048576,
        downloads_count: 45
    },
    {
        id: 2,
        title: "Manual de Administração Pública",
        description: "Conceitos fundamentais de administração pública para concursos",
        category: "Administração",
        file_size: 1536000,
        downloads_count: 32
    },
    {
        id: 3,
        title: "Manual de Português",
        description: "Gramática e interpretação de texto para concursos públicos",
        category: "Português",
        file_size: 3072000,
        downloads_count: 78
    }
];

function formatFileSize(bytes) {
    if (bytes >= 1048576) {
        return (bytes / 1048576).toFixed(2) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else {
        return bytes + ' bytes';
    }
}

function generateIndexHTML() {
    let manualsHTML = '';
    
    demoManuals.forEach(manual => {
        manualsHTML += `
            <div class="manual-card" data-title="${manual.title.toLowerCase()}" data-category="${manual.category.toLowerCase()}">
                <div class="manual-title">${manual.title}</div>
                <div class="manual-category">${manual.category}</div>
                <div class="manual-description">${manual.description}</div>
                <div class="manual-info">
                    <span>📥 ${manual.downloads_count} downloads</span>
                    <span>📄 ${formatFileSize(manual.file_size)}</span>
                </div>
                <button class="download-btn" onclick="downloadManual(${manual.id})">
                    ⬇️ Baixar Manual
                </button>
            </div>
        `;
    });

    return `
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biblioteca Digital CPA - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-notice {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #ffeaa7;
        }
        
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .manuals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .manual-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .manual-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .manual-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .manual-category {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            margin-bottom: 15px;
        }
        
        .manual-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .manual-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #888;
        }
        
        .download-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .download-btn:hover {
            opacity: 0.9;
        }
        
        .admin-link {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
        }
        
        .admin-link:hover {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .manuals-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="/admin" class="admin-link">🔐 Admin</a>
    
    <div class="container">
        <div class="header">
            <h1>📚 Biblioteca Digital CPA</h1>
            <p>Manuais de Concursos Públicos de Angola</p>
        </div>
        
        <div class="demo-notice">
            <strong>🚀 DEMO - Sistema Funcionando!</strong><br>
            Esta é uma demonstração do sistema. Em produção, conectaria ao banco MySQL remoto.
        </div>
        
        <div class="search-box">
            <input type="text" class="search-input" id="searchInput" placeholder="🔍 Pesquisar manuais...">
        </div>
        
        <div class="manuals-grid" id="manualsGrid">
            ${manualsHTML}
        </div>
    </div>
    
    <script>
        // Função de pesquisa
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.manual-card');
            
            cards.forEach(card => {
                const title = card.getAttribute('data-title');
                const category = card.getAttribute('data-category');
                
                if (title.includes(searchTerm) || category.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
        
        // Função para iniciar download
        function downloadManual(manualId) {
            alert('🔐 Em produção, você seria redirecionado para inserir o código de acesso.\\n\\nFluxo completo:\\n1. Inserir código\\n2. Se não tiver código → Assinar\\n3. Pagar e enviar comprovativo\\n4. Admin aprova\\n5. Código enviado por email\\n6. Download liberado por 30 dias');
        }
    </script>
</body>
</html>
    `;
}

function generateAdminHTML() {
    return `
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Demo - Biblioteca Digital CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .demo-notice {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #c3e6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .action-card h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .action-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            transition: opacity 0.3s;
        }
        
        .action-btn:hover {
            opacity: 0.9;
        }
        
        .urgent {
            border-left: 4px solid #ff6b6b;
        }
        
        .urgent .stat-number {
            color: #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>📊 Dashboard Administrativo</h1>
            <a href="/" class="back-btn">← Voltar ao Site</a>
        </div>
    </div>
    
    <div class="container">
        <div class="demo-notice">
            <strong>✅ PAINEL ADMIN DEMO</strong><br>
            Em produção, este painel conectaria ao banco MySQL e permitiria gerenciar todo o sistema.
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">Manuais Ativos</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">Total Assinantes</div>
            </div>
            
            <div class="stat-card urgent">
                <div class="stat-number">2</div>
                <div class="stat-label">Pagamentos Pendentes</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">Códigos Ativos</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">15</div>
                <div class="stat-label">Downloads Hoje</div>
            </div>
        </div>
        
        <div class="actions-grid">
            <div class="action-card urgent">
                <h3>💳 Verificar Pagamentos</h3>
                <p>Analise e aprove/rejeite pagamentos pendentes. Códigos são gerados automaticamente após aprovação.</p>
                <button class="action-btn" onclick="alert('Em produção: Visualizaria comprovativo, aprovaria/rejeitaria pagamento e geraria código automaticamente.')">
                    Verificar Pagamentos (2)
                </button>
            </div>
            
            <div class="action-card">
                <h3>📚 Gerenciar Manuais</h3>
                <p>Adicione, edite ou remova manuais da biblioteca. Controle quais manuais estão disponíveis para download.</p>
                <button class="action-btn" onclick="alert('Em produção: CRUD completo de manuais com upload de arquivos.')">
                    Gerenciar Manuais
                </button>
            </div>
            
            <div class="action-card">
                <h3>👥 Assinantes</h3>
                <p>Visualize informações dos assinantes, histórico de pagamentos e códigos gerados.</p>
                <button class="action-btn" onclick="alert('Em produção: Lista completa de assinantes com histórico.')">
                    Ver Assinantes
                </button>
            </div>
            
            <div class="action-card">
                <h3>📊 Relatórios</h3>
                <p>Acesse relatórios detalhados de downloads, pagamentos e uso da plataforma.</p>
                <button class="action-btn" onclick="alert('Em produção: Relatórios detalhados com gráficos e estatísticas.')">
                    Ver Relatórios
                </button>
            </div>
        </div>
    </div>
</body>
</html>
    `;
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (pathname === '/' || pathname === '/index.html') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateIndexHTML());
    } else if (pathname === '/admin') {
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(generateAdminHTML());
    } else {
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end('<h1>404 - Página não encontrada</h1><p><a href="/">Voltar ao início</a></p>');
    }
});

server.listen(PORT, () => {
    console.log(`🚀 Servidor de demonstração rodando em http://localhost:${PORT}`);
    console.log(`📚 Página principal: http://localhost:${PORT}`);
    console.log(`🔐 Painel admin: http://localhost:${PORT}/admin`);
    console.log(`\n✅ Sistema PHP completo criado e funcionando!`);
    console.log(`📁 Todos os arquivos estão prontos para produção.`);
});
