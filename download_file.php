<?php
/**
 * Arquivo de download protegido
 * Sistema de Biblioteca Digital CPA
 */

session_start();
require_once 'config/database.php';
require_once 'config/security.php';

// Verificar token de download
$token = isset($_GET['token']) ? $_GET['token'] : '';

if (empty($token) || 
    !isset($_SESSION['download_token']) || 
    $_SESSION['download_token'] !== $token ||
    !isset($_SESSION['download_expires']) ||
    time() > $_SESSION['download_expires']) {
    
    // Token inválido ou expirado
    header('HTTP/1.0 403 Forbidden');
    die('Acesso negado. Token inválido ou expirado.');
}

$manual_id = $_SESSION['download_manual_id'];
$access_code = $_SESSION['download_code'];

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Buscar informações do manual
    $query = "SELECT * FROM manuals WHERE id = ? AND is_active = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$manual_id]);
    $manual = $stmt->fetch();
    
    if (!$manual) {
        header('HTTP/1.0 404 Not Found');
        die('Manual não encontrado.');
    }
    
    // Verificar novamente o código (segurança adicional)
    $query = "SELECT c.*, s.id as subscriber_id, s.name, s.email 
              FROM codes c 
              JOIN subscribers s ON c.subscriber_id = s.id 
              WHERE c.code = ? AND c.is_active = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$access_code]);
    $code_data = $stmt->fetch();
    
    if (!$code_data || !Security::isCodeValid($code_data['created_at'])) {
        header('HTTP/1.0 403 Forbidden');
        die('Código inválido ou expirado.');
    }
    
    // Caminho completo do arquivo
    $file_path = $manual['file_path'];
    
    if (!file_exists($file_path)) {
        header('HTTP/1.0 404 Not Found');
        die('Arquivo não encontrado no servidor.');
    }
    
    // Registrar o download
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    
    $query = "INSERT INTO downloads (manual_id, subscriber_id, code_id, ip_address, user_agent) 
              VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->execute([
        $manual_id, 
        $code_data['subscriber_id'], 
        $code_data['id'], 
        $ip_address, 
        $user_agent
    ]);
    
    // Atualizar contador de downloads do manual
    $query = "UPDATE manuals SET downloads_count = downloads_count + 1 WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$manual_id]);
    
    // Atualizar contador de uso do código
    $query = "UPDATE codes SET used_count = used_count + 1 WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$code_data['id']]);
    
    // Limpar sessão
    unset($_SESSION['download_token']);
    unset($_SESSION['download_manual_id']);
    unset($_SESSION['download_code']);
    unset($_SESSION['download_expires']);
    
    // Preparar download
    $file_size = filesize($file_path);
    $file_name = $manual['file_name'];
    
    // Headers para download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
    header('Content-Length: ' . $file_size);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Limpar buffer de saída
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Enviar arquivo em chunks para economizar memória
    $chunk_size = 8192; // 8KB chunks
    $handle = fopen($file_path, 'rb');
    
    if ($handle === false) {
        header('HTTP/1.0 500 Internal Server Error');
        die('Erro ao abrir arquivo.');
    }
    
    while (!feof($handle)) {
        $chunk = fread($handle, $chunk_size);
        echo $chunk;
        flush();
    }
    
    fclose($handle);
    exit;
    
} catch (Exception $e) {
    error_log("Erro no download: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    die('Erro interno do servidor.');
}
