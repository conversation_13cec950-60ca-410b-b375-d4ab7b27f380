const mysql = require('mysql2/promise');
const fs = require('fs');

// Configurações do banco remoto
const dbConfig = {
    host: '*************',
    user: 'kacennua_bibliotecacpa',
    password: 'bibliotecacpa',
    database: 'kacennua_bibliotecacpa',
    charset: 'utf8mb4'
};

async function executeSQLScript() {
    console.log('🔧 Executando SQL no Banco MySQL Remoto');
    console.log('=====================================');
    console.log(`Host: ${dbConfig.host}`);
    console.log(`Banco: ${dbConfig.database}`);
    console.log(`Usuário: ${dbConfig.user}\n`);

    let connection;

    try {
        // Conectar ao banco
        console.log('📡 Conectando ao banco remoto...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Conexão estabelecida com sucesso!\n');

        // Ler o arquivo SQL
        console.log('📄 Lendo arquivo SQL...');
        const sqlContent = fs.readFileSync('database/create_tables.sql', 'utf8');
        console.log('✅ Arquivo SQL carregado!\n');

        // Dividir as queries por ponto e vírgula
        const queries = sqlContent.split(';').map(q => q.trim()).filter(q => q.length > 0 && !q.startsWith('--'));

        let successCount = 0;
        let errorCount = 0;

        console.log('🚀 Executando queries...');
        console.log('------------------------');

        for (let i = 0; i < queries.length; i++) {
            const query = queries[i];
            
            if (!query || query.startsWith('--')) continue;

            try {
                await connection.execute(query);
                successCount++;

                // Identificar o tipo de query
                if (query.toUpperCase().includes('CREATE TABLE')) {
                    const match = query.match(/CREATE TABLE.*?`?(\w+)`?/i);
                    const tableName = match ? match[1] : 'desconhecida';
                    console.log(`✅ Tabela '${tableName}' criada com sucesso`);
                } else if (query.toUpperCase().includes('INSERT INTO')) {
                    const match = query.match(/INSERT INTO.*?`?(\w+)`?/i);
                    const tableName = match ? match[1] : 'desconhecida';
                    console.log(`✅ Dados inseridos na tabela '${tableName}'`);
                } else if (query.toUpperCase().includes('CREATE INDEX')) {
                    const match = query.match(/CREATE INDEX.*?`?(\w+)`?/i);
                    const indexName = match ? match[1] : 'desconhecido';
                    console.log(`✅ Índice '${indexName}' criado`);
                } else {
                    console.log('✅ Query executada com sucesso');
                }

            } catch (error) {
                errorCount++;
                console.log(`❌ Erro na query ${i + 1}: ${error.message}`);
                
                // Mostrar parte da query que falhou
                const queryPreview = query.substring(0, 100) + (query.length > 100 ? '...' : '');
                console.log(`   Query: ${queryPreview}`);
            }
        }

        console.log('\n' + '='.repeat(50));
        console.log('📊 RESULTADO FINAL');
        console.log('='.repeat(50));
        console.log(`✅ Queries executadas com sucesso: ${successCount}`);
        console.log(`❌ Erros encontrados: ${errorCount}`);
        console.log(`📝 Total de queries processadas: ${queries.length}\n`);

        if (errorCount === 0) {
            console.log('🎉 BANCO DE DADOS CONFIGURADO COM SUCESSO!');
            console.log('Todas as tabelas foram criadas corretamente.\n');
        } else {
            console.log('⚠️  ALGUNS ERROS FORAM ENCONTRADOS');
            console.log('Algumas tabelas podem já existir (erro normal).\n');
        }

        // Verificar tabelas criadas
        console.log('🔍 Verificando tabelas criadas...');
        const [tables] = await connection.execute('SHOW TABLES');
        
        console.log('📋 Tabelas encontradas no banco:');
        tables.forEach(table => {
            const tableName = Object.values(table)[0];
            console.log(`   • ${tableName}`);
        });

        // Verificar admin padrão
        const tableNames = tables.map(table => Object.values(table)[0]);
        if (tableNames.includes('admins')) {
            console.log('\n👤 Verificando admin padrão...');
            const [adminRows] = await connection.execute("SELECT username FROM admins WHERE username = 'admin'");
            
            if (adminRows.length > 0) {
                console.log('✅ Admin padrão criado: admin');
                console.log('🔑 Senha padrão: admin123');
                console.log('⚠️  IMPORTANTE: Altere a senha após primeiro login!');
            } else {
                console.log('⚠️  Admin padrão não encontrado');
            }
        }

        // Verificar alguns dados de exemplo
        console.log('\n📊 Estatísticas do banco:');
        for (const tableName of ['manuals', 'subscribers', 'payments', 'codes', 'downloads']) {
            if (tableNames.includes(tableName)) {
                try {
                    const [countResult] = await connection.execute(`SELECT COUNT(*) as total FROM ${tableName}`);
                    console.log(`   • ${tableName}: ${countResult[0].total} registros`);
                } catch (error) {
                    console.log(`   • ${tableName}: erro ao contar registros`);
                }
            }
        }

        console.log('\n🚀 PRÓXIMOS PASSOS:');
        console.log('1. Acesse: http://localhost:3000 (demo) ou seu domínio');
        console.log('2. Teste a conexão: test_connection.php');
        console.log('3. Login admin: admin/login.php (admin/admin123)');
        console.log('4. Adicione alguns manuais de teste');
        console.log('5. Teste o fluxo completo de assinatura');

    } catch (error) {
        console.log(`❌ ERRO DE CONEXÃO: ${error.message}`);
        console.log('\nVerifique:');
        console.log('- Conectividade com o servidor remoto');
        console.log('- Credenciais do banco de dados');
        console.log('- Firewall/porta 3306');
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Conexão fechada.');
        }
    }

    console.log('\n' + '='.repeat(50));
    console.log('Script finalizado.');
}

// Executar o script
executeSQLScript().catch(console.error);
