<?php
/**
 * Script para executar SQL no banco MySQL remoto
 * Sistema de Biblioteca Digital CPA
 */

// Configurações do banco remoto
$host = '*************';
$db_name = 'kacennua_bibliotecacpa';
$username = 'kacennua_bibliotecacpa';
$password = 'bibliotecacpa';

echo "🔧 Executando SQL no Banco MySQL Remoto\n";
echo "=====================================\n";
echo "Host: $host\n";
echo "Banco: $db_name\n";
echo "Usuário: $username\n\n";

try {
    // Conectar ao banco
    echo "📡 Conectando ao banco remoto...\n";
    $conn = new PDO(
        "mysql:host=$host;dbname=$db_name;charset=utf8",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "✅ Conexão estabelecida com sucesso!\n\n";
    
    // Ler o arquivo SQL
    echo "📄 Lendo arquivo SQL...\n";
    $sql_content = file_get_contents('database/create_tables.sql');
    
    if (!$sql_content) {
        throw new Exception("Erro ao ler arquivo SQL");
    }
    
    echo "✅ Arquivo SQL carregado!\n\n";
    
    // Dividir as queries por ponto e vírgula
    $queries = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    $executed_queries = [];
    
    echo "🚀 Executando queries...\n";
    echo "------------------------\n";
    
    foreach ($queries as $index => $query) {
        $query = trim($query);
        
        // Pular queries vazias ou comentários
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $conn->exec($query);
            $success_count++;
            
            // Identificar o tipo de query
            if (stripos($query, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $query, $matches);
                $table_name = $matches[1] ?? 'desconhecida';
                echo "✅ Tabela '$table_name' criada com sucesso\n";
                $executed_queries[] = "CREATE TABLE $table_name";
            } elseif (stripos($query, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO.*?`?(\w+)`?/i', $query, $matches);
                $table_name = $matches[1] ?? 'desconhecida';
                echo "✅ Dados inseridos na tabela '$table_name'\n";
                $executed_queries[] = "INSERT INTO $table_name";
            } elseif (stripos($query, 'CREATE INDEX') !== false) {
                preg_match('/CREATE INDEX.*?`?(\w+)`?/i', $query, $matches);
                $index_name = $matches[1] ?? 'desconhecido';
                echo "✅ Índice '$index_name' criado\n";
                $executed_queries[] = "CREATE INDEX $index_name";
            } else {
                echo "✅ Query executada com sucesso\n";
                $executed_queries[] = "Query genérica";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "❌ Erro na query " . ($index + 1) . ": " . $e->getMessage() . "\n";
            
            // Mostrar parte da query que falhou (primeiros 100 caracteres)
            $query_preview = substr($query, 0, 100) . (strlen($query) > 100 ? '...' : '');
            echo "   Query: $query_preview\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 RESULTADO FINAL\n";
    echo str_repeat("=", 50) . "\n";
    echo "✅ Queries executadas com sucesso: $success_count\n";
    echo "❌ Erros encontrados: $error_count\n";
    echo "📝 Total de queries processadas: " . count($queries) . "\n\n";
    
    if ($error_count === 0) {
        echo "🎉 BANCO DE DADOS CONFIGURADO COM SUCESSO!\n";
        echo "Todas as tabelas foram criadas corretamente.\n\n";
        
        // Verificar tabelas criadas
        echo "🔍 Verificando tabelas criadas...\n";
        $stmt = $conn->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "📋 Tabelas encontradas no banco:\n";
        foreach ($tables as $table) {
            echo "   • $table\n";
        }
        
        // Verificar admin padrão
        if (in_array('admins', $tables)) {
            echo "\n👤 Verificando admin padrão...\n";
            $stmt = $conn->prepare("SELECT username FROM admins WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                echo "✅ Admin padrão criado: admin\n";
                echo "🔑 Senha padrão: admin123\n";
                echo "⚠️  IMPORTANTE: Altere a senha após primeiro login!\n";
            } else {
                echo "⚠️  Admin padrão não encontrado\n";
            }
        }
        
        echo "\n🚀 PRÓXIMOS PASSOS:\n";
        echo "1. Acesse: http://localhost:3000 (demo) ou seu domínio\n";
        echo "2. Teste a conexão: test_connection.php\n";
        echo "3. Login admin: admin/login.php (admin/admin123)\n";
        echo "4. Adicione alguns manuais de teste\n";
        echo "5. Teste o fluxo completo de assinatura\n";
        
    } else {
        echo "⚠️  ALGUNS ERROS FORAM ENCONTRADOS\n";
        echo "Verifique as mensagens de erro acima.\n";
        echo "Algumas tabelas podem já existir (erro normal).\n";
    }
    
} catch (PDOException $e) {
    echo "❌ ERRO DE CONEXÃO: " . $e->getMessage() . "\n";
    echo "\nVerifique:\n";
    echo "- Conectividade com o servidor remoto\n";
    echo "- Credenciais do banco de dados\n";
    echo "- Firewall/porta 3306\n";
} catch (Exception $e) {
    echo "❌ ERRO GERAL: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Script finalizado.\n";
?>
