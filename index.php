<?php
/**
 * Landing Page - Biblioteca Digital CPA
 * Lista todos os manuais disponíveis para download
 */

require_once 'config/database.php';

// Buscar manuais ativos
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT id, title, description, category, file_size, downloads_count, upload_date 
              FROM manuals 
              WHERE is_active = 1 
              ORDER BY category, title";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $manuals = $stmt->fetchAll();
    
} catch (Exception $e) {
    $manuals = [];
    $error_message = "Erro ao carregar manuais. Tente novamente mais tarde.";
}

// Função para formatar tamanho do arquivo
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biblioteca Digital CPA - Manuais de Concursos Públicos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .manuals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .manual-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .manual-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .manual-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .manual-category {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            margin-bottom: 15px;
        }
        
        .manual-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .manual-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #888;
        }
        
        .download-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .download-btn:hover {
            opacity: 0.9;
        }
        
        .no-manuals {
            text-align: center;
            color: white;
            font-size: 1.2em;
            margin-top: 50px;
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .manuals-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Biblioteca Digital CPA</h1>
            <p>Manuais de Concursos Públicos de Angola</p>
        </div>
        
        <?php if (isset($error_message)): ?>
            <div class="error-message">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>
        
        <div class="search-box">
            <input type="text" class="search-input" id="searchInput" placeholder="🔍 Pesquisar manuais...">
        </div>
        
        <div class="manuals-grid" id="manualsGrid">
            <?php if (empty($manuals)): ?>
                <div class="no-manuals">
                    <p>Nenhum manual disponível no momento.</p>
                </div>
            <?php else: ?>
                <?php foreach ($manuals as $manual): ?>
                    <div class="manual-card" data-title="<?php echo strtolower($manual['title']); ?>" data-category="<?php echo strtolower($manual['category']); ?>">
                        <div class="manual-title"><?php echo htmlspecialchars($manual['title']); ?></div>
                        
                        <?php if ($manual['category']): ?>
                            <div class="manual-category"><?php echo htmlspecialchars($manual['category']); ?></div>
                        <?php endif; ?>
                        
                        <?php if ($manual['description']): ?>
                            <div class="manual-description"><?php echo htmlspecialchars($manual['description']); ?></div>
                        <?php endif; ?>
                        
                        <div class="manual-info">
                            <span>📥 <?php echo $manual['downloads_count']; ?> downloads</span>
                            <?php if ($manual['file_size']): ?>
                                <span>📄 <?php echo formatFileSize($manual['file_size']); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <button class="download-btn" onclick="downloadManual(<?php echo $manual['id']; ?>)">
                            ⬇️ Baixar Manual
                        </button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Função de pesquisa
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.manual-card');
            
            cards.forEach(card => {
                const title = card.getAttribute('data-title');
                const category = card.getAttribute('data-category');
                
                if (title.includes(searchTerm) || category.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
        
        // Função para iniciar download
        function downloadManual(manualId) {
            window.location.href = 'manual.php?id=' + manualId;
        }
    </script>
</body>
</html>
