<?php
/**
 * Página de download de manual com validação de código
 * Sistema de Biblioteca Digital CPA
 */

session_start();
require_once 'config/database.php';
require_once 'config/security.php';

$manual_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error_message = '';
$success_message = '';

if ($manual_id <= 0) {
    header('Location: index.php');
    exit;
}

// Buscar informações do manual
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT * FROM manuals WHERE id = ? AND is_active = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$manual_id]);
    $manual = $stmt->fetch();
    
    if (!$manual) {
        header('Location: index.php');
        exit;
    }
    
} catch (Exception $e) {
    $error_message = "Erro ao carregar manual.";
}

// Processar validação do código
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['access_code'])) {
    $access_code = Security::sanitizeInput($_POST['access_code']);
    
    if (empty($access_code)) {
        $error_message = "Por favor, insira o código de acesso.";
    } else {
        try {
            // Verificar se o código existe e está válido
            $query = "SELECT c.*, s.id as subscriber_id, s.name, s.email 
                      FROM codes c 
                      JOIN subscribers s ON c.subscriber_id = s.id 
                      WHERE c.code = ? AND c.is_active = 1";
            $stmt = $conn->prepare($query);
            $stmt->execute([$access_code]);
            $code_data = $stmt->fetch();
            
            if (!$code_data) {
                $error_message = "Código inválido ou expirado.";
            } else {
                // Verificar se o código ainda está dentro da validade (30 dias)
                if (!Security::isCodeValid($code_data['created_at'])) {
                    $error_message = "Código expirado. Códigos são válidos por 30 dias.";
                } else {
                    // Código válido - redirecionar para download
                    // Criar token temporário para segurança adicional
                    $download_token = bin2hex(random_bytes(16));
                    $_SESSION['download_token'] = $download_token;
                    $_SESSION['download_manual_id'] = $manual_id;
                    $_SESSION['download_code'] = $access_code;
                    $_SESSION['download_expires'] = time() + 300; // 5 minutos

                    header("Location: download_file.php?token={$download_token}");
                    exit;
                }
            }
            
        } catch (Exception $e) {
            $error_message = "Erro ao validar código. Tente novamente.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download: <?php echo htmlspecialchars($manual['title']); ?> - Biblioteca Digital CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 600px;
            width: 90%;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .content {
            padding: 40px;
        }
        
        .manual-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .manual-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .manual-category {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            margin-bottom: 15px;
        }
        
        .manual-description {
            color: #666;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .submit-btn:hover {
            opacity: 0.9;
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 0 8px 8px 0;
        }
        
        .no-code-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .no-code-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        .no-code-link a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .container {
                width: 95%;
                margin: 20px auto;
            }
            
            .content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="index.php" class="back-btn">← Voltar</a>
            <h1>🔐 Acesso Restrito</h1>
            <p>Insira seu código para fazer o download</p>
        </div>
        
        <div class="content">
            <div class="manual-info">
                <div class="manual-title"><?php echo htmlspecialchars($manual['title']); ?></div>
                
                <?php if ($manual['category']): ?>
                    <div class="manual-category"><?php echo htmlspecialchars($manual['category']); ?></div>
                <?php endif; ?>
                
                <?php if ($manual['description']): ?>
                    <div class="manual-description"><?php echo htmlspecialchars($manual['description']); ?></div>
                <?php endif; ?>
            </div>
            
            <div class="info-box">
                <strong>ℹ️ Como obter o código:</strong><br>
                O código de acesso é enviado por email após a aprovação do seu pagamento. 
                Cada código é válido por 30 dias.
            </div>
            
            <?php if ($error_message): ?>
                <div class="error-message">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="access_code" class="form-label">Código de Acesso:</label>
                    <input type="text" 
                           id="access_code" 
                           name="access_code" 
                           class="form-input" 
                           placeholder="Digite seu código de 12 caracteres"
                           maxlength="12"
                           required>
                </div>
                
                <button type="submit" class="submit-btn">
                    🔓 Validar e Baixar
                </button>
            </form>
            
            <div class="no-code-link">
                <p>Não tem um código? <a href="subscribe.php">Assine agora</a></p>
            </div>
        </div>
    </div>
</body>
</html>
