<?php
/**
 * Página de assinatura - Processo de pagamento
 * Sistema de Biblioteca Digital CPA
 */

session_start();
require_once 'config/database.php';
require_once 'config/security.php';

$error_message = '';
$success_message = '';

// Processar formulário de assinatura
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = Security::sanitizeInput($_POST['name'] ?? '');
    $email = Security::sanitizeInput($_POST['email'] ?? '');
    $phone = Security::sanitizeInput($_POST['phone'] ?? '');
    $amount = Security::sanitizeInput($_POST['amount'] ?? '');
    $payment_method = Security::sanitizeInput($_POST['payment_method'] ?? '');
    
    // Validações
    if (empty($name) || empty($email) || empty($amount) || empty($payment_method)) {
        $error_message = "Todos os campos obrigatórios devem ser preenchidos.";
    } elseif (!Security::validateEmail($email)) {
        $error_message = "Email inválido.";
    } elseif (!is_numeric($amount) || $amount <= 0) {
        $error_message = "Valor inválido.";
    } else {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            // Verificar se email já existe
            $query = "SELECT id FROM subscribers WHERE email = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$email]);
            $existing_subscriber = $stmt->fetch();
            
            if ($existing_subscriber) {
                $subscriber_id = $existing_subscriber['id'];
            } else {
                // Criar novo assinante
                $query = "INSERT INTO subscribers (name, email, phone) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($query);
                $stmt->execute([$name, $email, $phone]);
                $subscriber_id = $conn->lastInsertId();
            }
            
            // Criar registro de pagamento
            $query = "INSERT INTO payments (subscriber_id, amount, payment_method, status) VALUES (?, ?, ?, 'pending')";
            $stmt = $conn->prepare($query);
            $stmt->execute([$subscriber_id, $amount, $payment_method]);
            $payment_id = $conn->lastInsertId();
            
            // Armazenar dados na sessão para o próximo passo
            $_SESSION['payment_id'] = $payment_id;
            $_SESSION['subscriber_name'] = $name;
            $_SESSION['subscriber_email'] = $email;
            
            // Redirecionar para upload de comprovativo
            header('Location: upload_receipt.php');
            exit;
            
        } catch (Exception $e) {
            $error_message = "Erro ao processar assinatura. Tente novamente.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assinar - Biblioteca Digital CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .pricing-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .price {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .price-description {
            color: #666;
            margin-bottom: 20px;
        }
        
        .benefits {
            list-style: none;
            text-align: left;
        }
        
        .benefits li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .benefits li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .payment-methods {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .payment-methods h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .bank-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .submit-btn:hover {
            opacity: 0.9;
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .required {
            color: #ff6b6b;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .price {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="index.php" class="back-btn">← Voltar</a>
            <h1>💳 Assinar Biblioteca Digital</h1>
            <p>Acesso completo aos manuais por 30 dias</p>
        </div>
        
        <div class="content">
            <div class="pricing-info">
                <div class="price">15.000 Kz</div>
                <div class="price-description">Acesso por 30 dias</div>
                
                <ul class="benefits">
                    <li>Download ilimitado de todos os manuais</li>
                    <li>Acesso a novos manuais adicionados</li>
                    <li>Suporte por email</li>
                    <li>Código válido por 30 dias</li>
                </ul>
            </div>
            
            <div class="payment-methods">
                <h3>💰 Métodos de Pagamento Aceitos:</h3>
                
                <div class="bank-info">
                    <strong>Banco BAI</strong><br>
                    Conta: *********<br>
                    IBAN: AO06 0040 0000 1234 5678 9012 3
                </div>
                
                <div class="bank-info">
                    <strong>Banco BIC</strong><br>
                    Conta: *********<br>
                    IBAN: AO06 0064 0000 9876 5432 1098 7
                </div>
                
                <div class="bank-info">
                    <strong>Multicaixa Express</strong><br>
                    Número: +244 900 000 000
                </div>
            </div>
            
            <?php if ($error_message): ?>
                <div class="error-message">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name" class="form-label">Nome Completo <span class="required">*</span></label>
                        <input type="text" id="name" name="name" class="form-input" required 
                               value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email <span class="required">*</span></label>
                        <input type="email" id="email" name="email" class="form-input" required
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone" class="form-label">Telefone</label>
                        <input type="tel" id="phone" name="phone" class="form-input"
                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="amount" class="form-label">Valor Pago (Kz) <span class="required">*</span></label>
                        <input type="number" id="amount" name="amount" class="form-input" required min="1" step="0.01"
                               value="<?php echo isset($_POST['amount']) ? htmlspecialchars($_POST['amount']) : '15000'; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="payment_method" class="form-label">Método de Pagamento <span class="required">*</span></label>
                    <select id="payment_method" name="payment_method" class="form-select" required>
                        <option value="">Selecione o método</option>
                        <option value="BAI" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] === 'BAI') ? 'selected' : ''; ?>>Banco BAI</option>
                        <option value="BIC" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] === 'BIC') ? 'selected' : ''; ?>>Banco BIC</option>
                        <option value="Multicaixa" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] === 'Multicaixa') ? 'selected' : ''; ?>>Multicaixa Express</option>
                    </select>
                </div>
                
                <button type="submit" class="submit-btn">
                    📄 Continuar para Upload do Comprovativo
                </button>
            </form>
        </div>
    </div>
</body>
</html>
