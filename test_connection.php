<?php
/**
 * Teste de Conexão com Banco de Dados
 * Sistema de Biblioteca Digital CPA
 */

require_once 'config/database.php';

echo "<h1>🔧 Teste de Conexão - Biblioteca Digital CPA</h1>";
echo "<hr>";

try {
    echo "<h2>📡 Testando Conexão com Banco Remoto...</h2>";
    
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✅ <strong>Conexão estabelecida com sucesso!</strong></p>";
        
        // Testar algumas queries básicas
        echo "<h3>🔍 Verificando Estrutura do Banco:</h3>";
        
        // Listar tabelas
        $query = "SHOW TABLES";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p style='color: orange;'>⚠️ <strong>Nenhuma tabela encontrada.</strong></p>";
            echo "<p>Execute o script de configuração: <code>php database/setup.php</code></p>";
        } else {
            echo "<p style='color: green;'>✅ <strong>Tabelas encontradas:</strong></p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>{$table}</li>";
            }
            echo "</ul>";
            
            // Verificar dados básicos
            echo "<h3>📊 Estatísticas Básicas:</h3>";
            
            $stats = [];
            
            foreach ($tables as $table) {
                try {
                    $query = "SELECT COUNT(*) as total FROM `{$table}`";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch();
                    $stats[$table] = $result['total'];
                } catch (Exception $e) {
                    $stats[$table] = 'Erro';
                }
            }
            
            echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>";
            echo "<tr><th>Tabela</th><th>Registros</th></tr>";
            foreach ($stats as $table => $count) {
                echo "<tr><td>{$table}</td><td>{$count}</td></tr>";
            }
            echo "</table>";
        }
        
        // Testar admin padrão
        if (in_array('admins', $tables)) {
            echo "<h3>👤 Verificando Admin Padrão:</h3>";
            $query = "SELECT username FROM admins WHERE username = 'admin'";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                echo "<p style='color: green;'>✅ <strong>Admin padrão encontrado:</strong> admin</p>";
                echo "<p><strong>Senha padrão:</strong> admin123</p>";
                echo "<p style='color: red;'>⚠️ <strong>Lembre-se de alterar a senha!</strong></p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Admin padrão não encontrado.</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ <strong>Falha na conexão!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>🔧 Verificações do Sistema:</h2>";

// Verificar extensões PHP
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'fileinfo'];
echo "<h3>📦 Extensões PHP:</h3>";
echo "<ul>";
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? "✅" : "❌";
    $color = $loaded ? "green" : "red";
    echo "<li style='color: {$color};'>{$status} {$ext}</li>";
}
echo "</ul>";

// Verificar diretórios
$required_dirs = ['uploads', 'uploads/receipts', 'files', 'files/manuals'];
echo "<h3>📁 Diretórios:</h3>";
echo "<ul>";
foreach ($required_dirs as $dir) {
    $exists = file_exists($dir);
    $writable = $exists ? is_writable($dir) : false;
    
    if ($exists && $writable) {
        echo "<li style='color: green;'>✅ {$dir} (existe e gravável)</li>";
    } elseif ($exists) {
        echo "<li style='color: orange;'>⚠️ {$dir} (existe mas não gravável)</li>";
    } else {
        echo "<li style='color: red;'>❌ {$dir} (não existe)</li>";
    }
}
echo "</ul>";

// Verificar arquivos de proteção
$protection_files = ['uploads/.htaccess', 'files/.htaccess'];
echo "<h3>🔒 Arquivos de Proteção:</h3>";
echo "<ul>";
foreach ($protection_files as $file) {
    $exists = file_exists($file);
    $status = $exists ? "✅" : "❌";
    $color = $exists ? "green" : "red";
    echo "<li style='color: {$color};'>{$status} {$file}</li>";
}
echo "</ul>";

echo "<hr>";
echo "<h2>🚀 Próximos Passos:</h2>";
echo "<ol>";
echo "<li>Se as tabelas não existem, execute: <code>php database/setup.php</code></li>";
echo "<li>Acesse o painel admin: <a href='admin/login.php'>admin/login.php</a></li>";
echo "<li>Faça login com: <strong>admin</strong> / <strong>admin123</strong></li>";
echo "<li>Adicione alguns manuais de teste</li>";
echo "<li>Teste o fluxo completo de assinatura</li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='index.php'>🏠 Ir para a página inicial</a> | <a href='admin/login.php'>🔐 Painel Admin</a></p>";
?>
