<?php
/**
 * Página de upload do comprovativo de pagamento
 * Sistema de Biblioteca Digital CPA
 */

session_start();
require_once 'config/database.php';
require_once 'config/security.php';

// Verificar se há dados de pagamento na sessão
if (!isset($_SESSION['payment_id']) || !isset($_SESSION['subscriber_name'])) {
    header('Location: subscribe.php');
    exit;
}

$payment_id = $_SESSION['payment_id'];
$subscriber_name = $_SESSION['subscriber_name'];
$subscriber_email = $_SESSION['subscriber_email'];

$error_message = '';
$success_message = '';

// Processar upload do comprovativo
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['receipt'])) {
    $upload_result = Security::validateUpload($_FILES['receipt']);
    
    if (!$upload_result['valid']) {
        $error_message = $upload_result['message'];
    } else {
        try {
            // Criar diretório se não existir
            $upload_dir = 'uploads/receipts/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Gerar nome seguro para o arquivo
            $secure_filename = Security::generateSecureFilename($_FILES['receipt']['name']);
            $upload_path = $upload_dir . $secure_filename;
            
            // Mover arquivo para diretório seguro
            if (move_uploaded_file($_FILES['receipt']['tmp_name'], $upload_path)) {
                // Atualizar registro de pagamento com o comprovativo
                $database = new Database();
                $conn = $database->getConnection();
                
                $query = "UPDATE payments SET receipt_filename = ?, receipt_path = ? WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->execute([$_FILES['receipt']['name'], $upload_path, $payment_id]);
                
                $success_message = "Comprovativo enviado com sucesso!";
                
                // Limpar dados da sessão
                unset($_SESSION['payment_id']);
                unset($_SESSION['subscriber_name']);
                unset($_SESSION['subscriber_email']);
                
            } else {
                $error_message = "Erro ao fazer upload do arquivo. Tente novamente.";
            }
            
        } catch (Exception $e) {
            $error_message = "Erro interno. Tente novamente mais tarde.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Comprovativo - Biblioteca Digital CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            width: 100%;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #1976d2;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f8ff;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            opacity: 0.9;
        }
        
        .submit-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .success-message {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .file-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .remove-file {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            text-align: center;
        }
        
        .modal h2 {
            color: #28a745;
            margin-bottom: 20px;
        }
        
        .modal-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .upload-area {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 Upload do Comprovativo</h1>
            <p>Envie seu comprovativo de pagamento</p>
        </div>
        
        <div class="content">
            <div class="user-info">
                <strong>👤 Assinante:</strong> <?php echo htmlspecialchars($subscriber_name); ?><br>
                <strong>📧 Email:</strong> <?php echo htmlspecialchars($subscriber_email); ?>
            </div>
            
            <div class="instructions">
                <h3>📋 Instruções:</h3>
                <ul>
                    <li>Envie uma foto clara do comprovativo de pagamento</li>
                    <li>Formatos aceitos: JPG, PNG ou PDF</li>
                    <li>Tamanho máximo: 5MB</li>
                    <li>Certifique-se que todos os dados estão visíveis</li>
                    <li>A verificação leva até 5 minutos</li>
                </ul>
            </div>
            
            <?php if ($error_message): ?>
                <div class="error-message">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="success-message">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$success_message): ?>
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            Arraste o arquivo aqui ou clique para selecionar
                        </div>
                        <button type="button" class="upload-btn" onclick="document.getElementById('receipt').click()">
                            Escolher Arquivo
                        </button>
                        <input type="file" id="receipt" name="receipt" class="file-input" accept=".jpg,.jpeg,.png,.pdf" required>
                    </div>
                    
                    <div class="file-preview" id="filePreview">
                        <div class="file-info">
                            <span id="fileName"></span>
                            <button type="button" class="remove-file" onclick="removeFile()">Remover</button>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn" disabled>
                        📤 Enviar Comprovativo
                    </button>
                </form>
            <?php else: ?>
                <div style="text-align: center; margin-top: 30px;">
                    <button class="modal-btn" onclick="showModal()">
                        ⏱️ Aguardar Verificação
                    </button>
                    <br><br>
                    <a href="index.php" style="color: #667eea; text-decoration: none;">← Voltar ao início</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Modal de sucesso -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <h2>✅ Comprovativo Enviado!</h2>
            <p>Seu comprovativo foi enviado com sucesso.</p>
            <p><strong>Aguarde até 5 minutos</strong> para a verificação.</p>
            <p>Você receberá seu código de acesso por email após a aprovação.</p>
            <button class="modal-btn" onclick="window.location.href='index.php'">
                Voltar ao Início
            </button>
        </div>
    </div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('receipt');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const submitBtn = document.getElementById('submitBtn');
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFilePreview(files[0]);
            }
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFilePreview(e.target.files[0]);
            }
        });
        
        function showFilePreview(file) {
            fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
            filePreview.style.display = 'block';
            submitBtn.disabled = false;
        }
        
        function removeFile() {
            fileInput.value = '';
            filePreview.style.display = 'none';
            submitBtn.disabled = true;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function showModal() {
            document.getElementById('successModal').style.display = 'block';
        }
        
        // Mostrar modal automaticamente se upload foi bem-sucedido
        <?php if ($success_message): ?>
            showModal();
        <?php endif; ?>
    </script>
</body>
</html>
